@article{pad<PERSON><PERSON>2023does,
  title={Does writing with language models reduce content diversity?},
  author={<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2309.05196},
  year={2023}
}

@article{lee2025doctalk,
  title={DocTalk: Scalable Graph-based Dialogue Synthesis for Enhancing LLM Conversational Capabilities},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, Sanket and Lockard, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, Haodong},
  journal={arXiv preprint arXiv:2507.05750},
  year={2025}
}

@book{em:86,
  editor  = "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>",
  title   = "Blackboard Systems",
  year    = 1986,
  address = "Reading, Mass.",
  publisher = "Addison-Wesley",
}

@inproceedings{c:83,
  author  = "<PERSON><PERSON>, <PERSON>",
  year    = 1983,
  title   = "{Communication, Simulation, and Intelligent
Agents: Implications of Personal Intelligent Machines
for Medical Education}",
  booktitle="Proceedings of the Eighth International Joint Conference on Artificial Intelligence {(IJCAI-83)}", 
  pages   = "556-560",
  address = "Menlo Park, Calif",
  publisher = "{IJCAI Organization}",
}

% Multi-Agent Collaboration in LLMs
@inproceedings{chen2023multi,
  author    = {<PERSON>, <PERSON>ang and Li, <PERSON>fan and <PERSON>, Zhen and Zhang, Ming},
  title     = {Multi-Agent Debate for Improved Problem Solving with Large Language Models},
  booktitle = {Proceedings of the 37th Conference on Neural Information Processing Systems},
  year      = {2023},
  pages     = {12456--12470},
  publisher = {Curran Associates, Inc.}
}

@article{du2023improving,
  title   = {Improving Factuality and Reasoning in Language Models through Multi-Agent Debate},
  author  = {Du, Yilun and Li, Shuang and Torralba, Antonio and Tenenbaum, Joshua B. and Mordatch, Igor},
  journal = {arXiv preprint arXiv:2305.14325},
  year    = {2023}
}

@inproceedings{wang2023enabling,
  author    = {Wang, Guanzhi and Xie, Yuqi and Jiang, Yunfan and Mandlekar, Ajay and Xiao, Chaowei and Zhu, Yuke and Fan, Linxi and Anandkumar, Anima},
  title     = {Voyager: An Open-Ended Embodied Agent with Large Language Models},
  booktitle = {Advances in Neural Information Processing Systems},
  year      = {2023},
  pages     = {31052--31067}
}

% LLM Reasoning and Benchmarks
@inproceedings{wei2022chain,
  author    = {Wei, Jason and Wang, Xuezhi and Schuurmans, Dale and Bosma, Maarten and Chi, Ed and Le, Quoc and Zhou, Denny},
  title     = {Chain-of-Thought Prompting Elicits Reasoning in Large Language Models},
  booktitle = {Advances in Neural Information Processing Systems},
  year      = {2022},
  pages     = {24824--24837}
}

@article{cobbe2021training,
  author    = {Cobbe, Karl and Kosaraju, Vineet and Bavarian, Mohammad and Chen, Mark and Jun, Heewoo and Kaiser, Lukasz and Plappert, Matthias and Tworek, Jerry and Hilton, Jacob and Nakano, Reiichiro and Hesse, Christopher and Schulman, John},
  title     = {Training Verifiers to Solve Math Word Problems},
  journal   = {arXiv preprint arXiv:2110.14168},
  year      = {2021}
}

@article{hendrycks2021measuring,
  author    = {Hendrycks, Dan and Burns, Collin and Basart, Steven and Zou, Andy and Mazeika, Mantas and Song, Dawn and Steinhardt, Jacob},
  title     = {Measuring Massive Multitask Language Understanding},
  journal   = {arXiv preprint arXiv:2009.03300},
  year      = {2020}
}

@article{hendrycks2021math,
  author    = {Hendrycks, Dan and Burns, Collin and Kadavath, Saurav and Zhu, Eric and Basart, Steven and Steiner, Jacob and Song, Dawn and Goldblum, Micah},
  title     = {Measuring Mathematical Problem Solving with the MATH Dataset},
  journal   = {arXiv preprint arXiv:2103.03874},
  year      = {2021}
}

@article{dhingra2022gpqa,
  author    = {Dhingra, Bhuwan and Mihaylov, Todor and Welleck, Sean and Jo, Yanai and Bahdanau, Dzmitry},
  title     = {GPQA: A Benchmark for Graduate-Level Physics Question Answering},
  journal   = {arXiv preprint arXiv:2209.05730},
  year      = {2022}
}

@inproceedings{geva2021strategyqa,
  author    = {Geva, Mor and Khashabi, Daniel and Gabriel, Uri and Berant, Jonathan},
  title     = {StrategyQA: A Benchmark for Implicit Reasoning Strategies},
  booktitle = {Proceedings of the 2021 Conference of the North American Chapter of the Association for Computational Linguistics},
  year      = {2021},
  pages     = {4028--4039}
}

@inproceedings{chen2021evaluating,
  author    = {Chen, Mark and Tworek, Jerry and Jun, Heewoo and Yuan, Qiming and Pinto, Henrique Ponde de Oliveira and Kaplan, Jared and Edwards, Harri and Burda, Yuri and Joseph, Nicholas and Brockman, Greg and Ray, Alex and Puri, Raul and Krueger, Gretchen and Petrov, Michael and Khlaaf, Heidy and Sastry, Girish and Askell, Amanda and Rudolph, Sandini and Wu, Jeff and Tezak, Lilian and Tang, Liane and Babuschkin, Igor and Powell, Kate and Rush, Ruslan and Thomas, Karthik and Sigler, Benjamin and Cai, Tianyi and Wainwright, Martin and Ha, David and Neumann, Arvind and Huizinga, Joost},
  title     = {Evaluating Large Language Models Trained on Code},
  booktitle = {arXiv preprint arXiv:2107.03374},
  year      = {2021}
}

@inproceedings{austin2021program,
  author    = {Austin, Jacob and Odena, Augustus and Nye, Maxwell and Bosma, Maarten and Michalewski, Henryk and Dohan, David and Jiang, Ellen and Cai, Carrie and Terry, Michael and Le, Quoc and Sutton, Charles},
  title     = {Program Synthesis with Large Language Models},
  booktitle = {arXiv preprint arXiv:2108.07732},
  year      = {2021}
}

% Reading Comprehension and QA


@inproceedings{yang2018hotpotqa,
  author    = {Yang, Zhilin and Qi, Peng and Zhang, Saizheng and Bengio, Yoshua and Cohen, William and Salakhutdinov, Ruslan and Manning, Christopher D.},
  title     = {HotpotQA: A Dataset for Diverse, Explainable Multi-hop Question Answering},
  booktitle = {Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing},
  year      = {2018},
  pages     = {2369--2380}
}

% Multi-Agent Systems (Classical)
@article{stone2000multiagent,
  author    = {Stone, Peter and Veloso, Manuela},
  title     = {Multiagent Systems: A Survey from a Machine Learning Perspective},
  journal   = {Autonomous Robots},
  year      = {2000},
  volume    = {8},
  number    = {3},
  pages     = {345--383}
}

@book{wooldridge2009introduction,
  author    = {Wooldridge, Michael},
  title     = {An Introduction to MultiAgent Systems},
  year      = {2009},
  publisher = {John Wiley \& Sons},
  edition   = {2nd}
}

% Consensus and Coordination
@article{olfati2007consensus,
  author    = {Olfati-Saber, Reza and Fax, J. Alex and Murray, Richard M.},
  title     = {Consensus and Cooperation in Networked Multi-Agent Systems},
  journal   = {Proceedings of the IEEE},
  year      = {2007},
  volume    = {95},
  number    = {1},
  pages     = {215--233}
}

% Recent LLM developments
@article{brown2020language,
  author    = {Brown, Tom and Mann, Benjamin and Ryder, Nick and Subbiah, Melanie and Kaplan, Jared D and Dhariwal, Prafulla and Neelakantan, Arvind and Shyam, Pranav and Sastry, Girish and Askell, Amanda and others},
  title     = {Language Models are Few-Shot Learners},
  journal   = {Advances in neural information processing systems},
  year      = {2020},
  volume    = {33},
  pages     = {1877--1901}
}

@article{ouyang2022training,
  author    = {Ouyang, Long and Wu, Jeffrey and Jiang, Xu and Almeida, Diogo and Wainwright, Carroll and Mishkin, Pamela and Zhang, Chong and Agarwal, Sandhini and Slama, Katarina and Ray, Alex and others},
  title     = {Training language models to follow instructions with human feedback},
  journal   = {Advances in Neural Information Processing Systems},
  year      = {2022},
  volume    = {35},
  pages     = {27730--27744}
}

@inproceedings{c:84,
  author  = "Clancey, William J.",
  year    = 1984,
  title   = "{Classification Problem Solving}",
  booktitle = "Proceedings of the Fourth National 
              Conference on Artificial Intelligence",
  pages   = "45-54",
  address = "Menlo Park, Calif.",
  publisher="AAAI Press",
}

@article{r:80,
  author = {Robinson, Arthur L.},
  title = {New Ways to Make Microcircuits Smaller},
  volume = {208},
  number = {4447},
  pages = {1019--1022},
  year = {1980},
  doi = {10.1126/science.208.4447.1019},
  publisher = {American Association for the Advancement of Science},
  issn = {0036-8075},
  URL = {https://science.sciencemag.org/content/208/4447/1019},
  eprint = {https://science.sciencemag.org/content/208/4447/1019.full.pdf},
  journal = {Science},
}
@article{r:80x,
  author  = "Robinson, Arthur L.",
  year    = 1980,
  title   = "{New Ways to Make Microcircuits Smaller---Duplicate Entry}",
  journal = "Science",
  volume  =  208,
  pages   = "1019-1026",
}
@article{hcr:83,
title = {Strategic explanations for a diagnostic consultation system},
journal = {International Journal of Man-Machine Studies},
volume = {20},
number = {1},
pages = {3-19},
year = {1984},
issn = {0020-7373},
doi = {https://doi.org/10.1016/S0020-7373(84)80003-6},
url = {https://www.sciencedirect.com/science/article/pii/S0020737384800036},
author = {Diane Warner Hasling and William J. Clancey and Glenn Rennels},
abstract = {This article examines the problem of automatte explanation of reasoning, especially as it relates to expert systems. By explanation we mean the ability of a program to discuss what it is doing in some understandable way. We first present a general framework in which to view explanation and review some of the research done in this area. We then focus on the explanation system for NEOMYCIN, a medical consultation program. A consultation program interactively helps a user to solve a problem. Our goal is to have NEOMYCIN explain its problem-solving strategies. An explanation of strategy describes the plan the program is using to reach a solution. Such an explanation is usually concrete, referring to aspects of the current problem situation. Abstract explanations articulate a general principle, which can be applied in different situations; such explanations are useful in teaching and in explaining by analogy. We describe the aspects of NEOMYCIN that make abstract strategic explanations possible—the representation of strategic knowledge explicitly and separately from domain knowledge— and demonstrate how this representation can be used to generate explanations.}
}

@techreport{r:86,
  author  = "Rice, James",
  year    = 1986,
  title   = "{Poligon: A System for Parallel Problem Solving}",
  type    = "Technical Report", 
  number  = "KSL-86-19", 
  institution = "Dept.\ of Computer Science, Stanford Univ.",
}
@phdthesis{c:79,
  author  = "Clancey, William J.",
  year    = 1979,
  title   = "{Transfer of Rule-Based Expertise
through a Tutorial Dialogue}",
  type    = "{Ph.D.} diss.",
  school  = "Dept.\ of Computer Science, Stanford Univ.",
  address = "Stanford, Calif.",
}
@unpublished{c:21,
  author  = "Clancey, William J.",
  title   = "{The Engineering of Qualitative Models}",
  year    = 2021,
  note    = "Forthcoming",
}
@misc{c:22,
      title={Attention Is All You Need}, 
      author={Ashish Vaswani and Noam Shazeer and Niki Parmar and Jakob Uszkoreit and Llion Jones and Aidan N. Gomez and Lukasz Kaiser and Illia Polosukhin},
      year={2017},
      eprint={1706.03762},
      archivePrefix={arXiv},
      primaryClass={cs.CL}
}
@misc{c:23,
  title        = "Pluto: The 'Other' Red Planet",
  author       = "{NASA}",
  howpublished = "\url{https://www.nasa.gov/nh/pluto-the-other-red-planet}",
  year         = 2015,
  note         = "Accessed: 2018-12-06"
}

@article{yao2024tree,
  title   = {Tree of Thoughts: Deliberate Problem Solving with Large Language Models},
  author  = {Yao, Shunyu and Yu, Dian and Zhao, Jeffrey and Shafran, Izhak and Griffiths, Tom and Cao, Yuan and Narasimhan, Karthik},
  journal = {Advances in Neural Information Processing Systems},
  year    = {2024},
  note    = {arXiv:2305.10601}
}

@article{besta2024graph,
  title   = {Graph of Thoughts: Solving Elaborate Problems with Large Language Models},
  author  = {Besta, Maciej and Blach, Nils and Kubicek, Ales and Gerstenberger, Robert and Podstawski, Michal and Gianinazzi, Lukas and others},
  journal = {Proceedings of the AAAI Conference on Artificial Intelligence},
  year    = {2024},
  note    = {arXiv:2308.08614}
}

@article{zhang2024aflow,
  title   = {AFLOW: Automating Agentic Workflow Generation},
  author  = {Zhang, Jiayi and Xiang, Jinyu and Yu, Zhaoyang and Teng, Fengwei and Chen, Xiong-Hui and Chen, Jiaqi and others},
  year    = {2024},
  note    = {arXiv:2410.10762}
}

@article{frendo2023gptswarm,
  title   = {GPT-Swarm: Multi-Agent Simulation Framework for Complex Problem Solving},
  author  = {Frendo, Kyle and Amiri, Hamidreza and Rawassizadeh, Reza},
  journal = {arXiv preprint},
  year    = {2023},
  note    = {arXiv:2305.19686}
}

### Multi-Agent Debate

```bibtex
@article{li2023camel,
  title   = {CAMEL: Communicative Agents for "Mind" Exploration of Large Scale Language Model Society},
  author  = {Li, Guohao and Mendi, Hasan and Wang, Shui and and others},
  journal = {arXiv preprint arXiv:2303.17760},
  year    = {2023}
}
```
*   **说明**: 这是 "CAMEL" (Communicative Agents) 的原始论文，常被用作角色扮演式多智能体协作的代表。

@article{xu2025cod,
  title   = {Chain of Draft: Thinking Faster by Writing Less},
  author  = {Xu, Silei and Xie, Wenhao and Zhao, Lingxiao and He, Pengcheng},
  journal = {arXiv preprint arXiv:2502.18600},
  year    = {2025}
}

@article{zhang2024cut,
  title={Cut the crap: An economical communication pipeline for llm-based multi-agent systems},
  author={Zhang, Guibin and Yue, Yanwei and Li, Zhixun and Yun, Sukwon and Wan, Guancheng and Wang, Kun and Cheng, Dawei and Yu, Jeffrey Xu and Chen, Tianlong},
  journal={arXiv preprint arXiv:2410.02506},
  year={2024}
}

@inproceedings{liu2024dynamic,
  title={A dynamic llm-powered agent network for task-oriented agent collaboration},
  author={Liu, Zijun and Zhang, Yanzhe and Li, Peng and Liu, Yang and Yang, Diyi},
  booktitle={First Conference on Language Modeling},
  year={2024}
}

@article{cemri2025multi,
  title={Why Do Multi-Agent LLM Systems Fail?},
  author={Cemri, Mert and Pan, Melissa Z. and Yang, Shuyi and Agrawal, Lakshya A. and Chopra, Bhavya and Tiwari, Rishabh and Keutzer, Kurt and Parameswaran, Aditya and Klein, Dan and Ramchandran, Kannan and Zaharia, Matei and Gonzalez, Joseph E. and Stoica, Ion},
  journal={arXiv preprint arXiv:2503.13657},
  year={2025}
}
