%File: anonymous-submission-latex-2026.tex
\documentclass[letterpaper]{article} % DO NOT CHANGE THIS
\usepackage[submission]{aaai2026}  % DO NOT CHANGE THIS
\usepackage{times}  % DO NOT CHANGE THIS
\usepackage{helvet}  % DO NOT CHANGE THIS
\usepackage{courier}  % DO NOT CHANGE THIS
\usepackage[hyphens]{url}  % DO NOT CHANGE THIS
\usepackage{graphicx} % DO NOT CHANGE THIS
\urlstyle{rm} % DO NOT CHANGE THIS
\def\UrlFont{\rm}  % DO NOT CHANGE THIS
\usepackage{natbib}  % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\usepackage{caption} % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\frenchspacing  % DO NOT CHANGE THIS
\setlength{\pdfpagewidth}{8.5in} % DO NOT CHANGE THIS
\setlength{\pdfpageheight}{11in} % DO NOT CHANGE THIS
%
% These are recommended to typeset algorithms but not required. See the subsubsection on algorithms. Remove them if you don't have algorithms in your paper.
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{microtype}
\usepackage{amsthm}
\usepackage[T1]{fontenc}
\usepackage{textcomp}
\theoremstyle{definition}
\newtheorem{lemma}{Lemma}
\newtheorem{theorem}{Theorem}

%
% These are are recommended to typeset listings but not required. See the subsubsection on listing. Remove this block if you don't have listings in your paper.
\usepackage{newfloat}
\usepackage{listings}
\DeclareCaptionStyle{ruled}{labelfont=normalfont,labelsep=colon,strut=off} % DO NOT CHANGE THIS
\lstset{%
	basicstyle={\footnotesize\ttfamily},% footnotesize acceptable for monospace
	numbers=left,numberstyle=\footnotesize,xleftmargin=2em,% show line numbers, remove this entire line if you don't want the numbers.
	aboveskip=0pt,belowskip=0pt,%
	showstringspaces=false,tabsize=2,breaklines=true,
	captionpos=b,floatplacement=tb,abovecaptionskip=0pt,belowskip=0pt}
\captionsetup[listing]{labelfont=bf,singlelinecheck=false}
\floatstyle{ruled}
\newfloat{listing}{tb}{lst}{}
\floatname{listing}{Listing}
%
% Keep the \pdfinfo as shown here. There's no need
% for you to add the /Title and /Author tags.
\pdfinfo{
/TemplateVersion (2026.1)
}

% DISALLOWED PACKAGES
% \usepackage{authblk} -- This package is specifically forbidden
% \usepackage{balance} -- This package is specifically forbidden
% \usepackage{color (if used in text)
% \usepackage{CJK} -- This package is specifically forbidden
% \usepackage{float} -- This package is specifically forbidden
% \usepackage{flushend} -- This package is specifically forbidden
% \usepackage{fontenc} -- This package is specifically forbidden
% \usepackage{fullpage} -- This package is specifically forbidden
% \usepackage{geometry} -- This package is specifically forbidden
% \usepackage{grffile} -- This package is specifically forbidden
% \usepackage{hyperref} -- This package is specifically forbidden
% \usepackage{navigator} -- This package is specifically forbidden
% (or any other package that embeds links such as navigator or hyperref)
% \indentfirst} -- This package is specifically forbidden
% \layout} -- This package is specifically forbidden
% \multicol} -- This package is specifically forbidden
% \nameref} -- This package is specifically forbidden
% \usepackage{savetrees} -- This package is specifically forbidden
% \usepackage{setspace} -- This package is specifically forbidden
% \usepackage{stfloats} -- This package is specifically forbidden
% \usepackage{tabu} -- This package is specifically forbidden
% \usepackage{titlesec} -- This package is specifically forbidden
% \usepackage{tocbibind} -- This package is specifically forbidden
% \usepackage{ulem} -- This package is specifically forbidden
% \usepackage{wrapfig} -- This package is specifically forbidden
% DISALLOWED COMMANDS
% \nocopyright -- Your paper will not be published if you use this command
% \addtolength -- This command may not be used
% \balance -- This command may not be used
% \baselinestretch -- Your paper will not be published if you use this command
% \clearpage -- No page breaks of any kind may be used for the final version of your paper
% \columnsep -- This command may not be used
% \newpage -- No page breaks of any kind may be used for the final version of your paper
% \pagebreak -- No page breaks of any kind may be used for the final version of your paperr
% \pagestyle -- This command may not be used
% \tiny -- This is not an acceptable font size.
% \vspace{- -- No negative value may be used in proximity of a caption, figure, table, section, subsection, subsubsection, or reference
% \vskip{- -- No negative value may be used to alter spacing above or below a caption, figure, table, section, subsection, subsubsection, or reference

\setcounter{secnumdepth}{1} %Changed from 0 to 1 to enable section numbering for better navigation

% The file aaai2026.sty is the style file for AAAI Press
% proceedings, working notes, and technical reports.
%

% Title
\title{Draft-Mediated Collaboration: A Context Engineering Approach for Multi-Agent LLM Systems}
\author{
    Anonymous Submission
}
\affiliations{
    Anonymous Institution
}

\begin{document}

\maketitle

% --------- Polished Abstract ---------
\begin{abstract}
Multi-agent LLM systems that rely on open-ended dialogue suffer from context explosion, topic drift, and high coordination cost. We present Draft-Mediated Collaboration (DMC), an artifact-centric protocol in which agents annotate a shared draft instead of exchanging unbounded messages. DMC enforces schema-constrained edits and a coordinator--merge loop that bounds per-round communication to $O(A \cdot N + A \cdot L)$ and yields total complexity $O(R \cdot (A \cdot N + A \cdot L))$. Across eight reasoning benchmarks, DMC reduces input tokens by up to 85.6\% and wall-clock latency by up to 51.5\% vs. baseline methods, while matching or surpassing strong baselines (e.g., 98\% on GSM8K, 100\% on MBPP evaluated on 100 problems each). Communication scaling analysis reveals DMC's linear growth (1,090 tokens/round average) versus quadratic scaling in dialogue-based systems (4,889 tokens/round average). Ablations show that disciplined context control---rather than additional dialogue---drives the gains, and theory explains the linear scaling in agent count. These results suggest that the most effective multi-agent collaboration is not the most talkative: structuring interaction around drafts yields predictable resource use and strong accuracy.
\end{abstract}

% --------- Introduction (already polished) ---------
\section{Introduction}

The capacity of Large Language Models (LLMs) to act as autonomous agents has unlocked new possibilities for solving complex, multi-faceted problems \cite{brown2020language, ouyang2022training}. A natural evolution is to form teams of LLM agents, hoping to harness collective intelligence. However, the predominant collaboration paradigm---direct, open-ended dialogue---faces critical scalability challenges that undermine its potential. As agents converse, the shared context expands uncontrollably, leading to four fundamental challenges: 1)~\textbf{Context Explosion}, where history quickly exceeds model limits; 2)~\textbf{Topic Drift}, where conversations lose focus; 3)~\textbf{Communication Overhead}, with complexity scaling with dialogue length; and 4)~\textbf{Information Dilution}, where key insights are buried in verbose exchanges.

Existing approaches offer an unsatisfactory trade-off. On one hand, dialogue-based systems like multi-agent debate \cite{du2023improving, chen2023multi} demonstrate the power of multiple perspectives but suffer from communication challenges and coordination difficulties. Recent empirical analysis reveals the severity of this problem: Cemri et al.~\cite{cemri2025multi} found that popular multi-agent frameworks exhibit failure rates as high as 75\%, with many failures stemming from inter-agent communication breakdowns rather than individual agent limitations. Their systematic study of 14 failure modes across 150+ conversation traces demonstrates that current multi-agent systems suffer from fundamental design flaws in how agents coordinate and communicate.

Much of the recent work on LLM reasoning, such as Chain-of-Thought~\cite{wei2022chain} and its more complex variants like Tree-of-Thoughts~\cite{yao2024tree}, has focused on structuring the internal thought process of a *single agent*. However, when multiple agents collaborate, the bottleneck shifts from individual reasoning to inter-agent communication. We argue that optimizing the communication protocol is as critical as optimizing the reasoning topology. Just as an effective human team relies on a concise, shared document (like a meeting minute or a project draft) rather than verbose, unstructured discussion, we posit that multi-agent LLM systems can achieve better coordination by shifting their interaction model from dialogue to a shared artifact.

To bridge this gap, we propose a fundamentally different approach: \textbf{Draft-Mediated Collaboration (DMC)}. Instead of agents communicating directly *with each other*, they interact indirectly *through a shared draft*. This draft serves as a centralized, structured artifact that embodies the current state of the solution. Communication shifts from verbose natural language exchanges to concise, targeted operations on the draft, such as proposing edits, adding annotations, or verifying facts. This paradigm inherently enforces focus, structures interaction, and contains context growth.

Our work makes the following contributions:

\textbf{A Draft-Mediated Collaboration Protocol:} We formalize and implement a structured protocol where a shared draft serves as the central coordination mechanism for multi-agent reasoning tasks, addressing communication complexity challenges in problem-solving scenarios.

\textbf{Efficient Specialised Agent Roles:} We design a lean set of three specialized agents---Workers, a Merger, and a Leader---each with a distinct function that contributes to the draft's evolution without redundant communication. These agents are coordinated by a central orchestration framework, as illustrated in Figure~\ref{fig:architecture}.

\textbf{Structured Annotation Protocol:} We formalize a bounded annotation system that transforms verbose multi-agent dialogue into concise, targeted feedback operations, fundamentally changing how agents communicate and coordinate.

\textbf{Collaboration Efficiency Analysis:} We demonstrate that draft-mediated collaboration achieves linear scaling compared to quadratic growth in dialogue-based schemes, with empirical validation across diverse benchmarks.

\textbf{Systematic Failure Mode Prevention:} We develop a principled framework that structurally prevents common multi-agent system failures through artifact-centric design, addressing critical failure modes identified in recent taxonomies while maintaining coordination effectiveness.

\textbf{Comprehensive Empirical Validation:} We conduct extensive experiments on eight diverse benchmarks (100 problems each), demonstrating that DMC achieves strong performance (including 98.0\% on GSM8K, 100\% on MBPP) with structured communication patterns, often outperforming prominent baselines (including CoT, CoD, and LLM-Debate).

\begin{figure}[t]
\centering
\includegraphics[width=0.9\columnwidth]{dmc_architecture.png}
\caption{DMC System Architecture: Three specialized agents collaborate through a shared draft under centralized orchestration. Workers generate initial solutions and provide structured annotations, the Merger integrates feedback into draft updates, and the Leader evaluates quality for termination decisions. The orchestration framework ensures bounded communication and efficient coordination.}
\label{fig:architecture}
\end{figure}

\section{Related Work}

\textbf{Context Engineering in LLMs:} Recent advances have focused on leveraging longer contexts effectively \cite{brown2020language}. However, longer contexts introduce challenges including attention dilution and difficulty maintaining focus on specific tasks. Our work addresses these challenges specifically in multi-agent settings.

\textbf{Multi-Agent LLM Collaboration:} Existing approaches typically involve direct agent communication through extended dialogues \cite{chen2023multi,du2023improving}. While these methods can improve reasoning quality, they suffer from context explosion and coordination challenges. Chen et al. \cite{chen2023multi} demonstrated multi-agent debate effectiveness but acknowledged the communication overhead of lengthy exchanges.

Recent work by Cemri et al. \cite{cemri2025multi} provides crucial insights into why multi-agent systems fail, identifying 14 distinct failure modes organized into three categories: specification and system design failures, inter-agent misalignment, and task verification and termination issues. Their Multi-Agent System Failure Taxonomy (MASFT) reveals that many failures stem from inter-agent communication challenges rather than individual agent limitations, with failure rates as high as 75\% in popular frameworks like ChatDev. This systematic analysis underscores the critical need for structured communication protocols that can prevent common failure patterns such as conversation reset, task derailment, and incomplete verification.

Recent variants further explore structured reasoning topologies, including Tree\nobreakdash-of\nobreakdash-Thoughts~\cite{yao2024tree} and Graph\nobreakdash-of\nobreakdash-Thoughts~\cite{besta2024graph}. Automated workflow discovery like AFLOW~\cite{zhang2024aflow} and decentralised agent collectives such as GPT\nobreakdash-Swarm~\cite{frendo2023gptswarm} offer orthogonal perspectives on orchestrating multiple LLM calls.

\textbf{Document-Driven Collaboration Methods:} Recent work has explored document-centric collaboration but with different objectives. Padmakumar and He~\cite{padmakumar2023does} focus on human-AI creative writing, while DocTalk~\cite{lee2025doctalk} uses documents for dialogue synthesis training. DMC differs by targeting structured multi-agent reasoning with bounded annotations rather than creative writing or dialogue generation.

\textbf{Structured Communication in AI:} Classical multi-agent systems \cite{stone2000multiagent,wooldridge2009introduction} have explored structured communication protocols, but these approaches were designed for symbolic reasoning rather than natural language collaboration. Our work bridges structured communication principles with modern LLM capabilities, specifically addressing the communication explosion problem in multi-agent reasoning tasks.

% ---------- NEW SECTION: Problem Definition ----------
\section{Problem Definition}
We formalize multi-agent collaboration as an iterative draft refinement process. Let $q$ denote a task prompt and $A=\{a_1,\dots,a_{|A|}\}$ the set of LLM agents. The system maintains a shared draft $d^{(r)}$ at round $r$, which evolves through structured phases:

\begin{enumerate}
    \item \textbf{Draft Generation}: Workers create initial solution candidates in parallel
    \item \textbf{Annotation}: Each agent produces structured feedback $\alpha_i^{(r)}$ with bounded length
    \item \textbf{Adaptive Merge}: An intelligent merger synthesizes annotations into $d^{(r+1)}$
    \item \textbf{Quality Assessment}: A leader evaluates draft quality and decides continuation
\end{enumerate}

The collaboration terminates when quality score $\mathcal{Q}(d^{(r)}) \geq \tau_t$ for task type $t$, or after reaching maximum rounds $R_{\max}$.

The objective maximizes answer accuracy while maintaining communication efficiency:
\begin{align}
\max_{A,\mathcal{M},\mathcal{Q}} &\mathbb{E}_{q \sim \mathcal{D}}[\text{Acc}(d^{(\leq R)}, q)] \nonumber \\
&\text{s.t. communication overhead is bounded} \nonumber
\end{align}

Our framework employs task-adaptive parameters: annotation limits $L_t \in [100, 150]$ tokens and quality thresholds $\tau_t \in [0.65, 0.95]$ vary by task complexity.

This structured approach contrasts with dialogue-based schemes where communication grows quadratically with agents and rounds.

\section{Method}
\label{sec:method}



Our draft-mediated collaboration framework addresses context engineering challenges through structured, indirect agent communication. Instead of lengthy multi-agent dialogues, agents collaborate around concise drafts using targeted annotations and intelligent synthesis.

\subsection{Conceptual Framework}

Our approach differs from dialogue-based multi-agent systems by using shared drafts as communication intermediates. Instead of agents exchanging unbounded messages, they collaborate through bounded annotations on evolving drafts. This shift from dialogue sequences to artifact evolution enables predictable resource consumption and structured coordination patterns.

\subsection{Draft-Mediated Collaboration Architecture}

\subsubsection{Core Design Principles}
Our framework is built on three key principles that address context engineering challenges:

\textbf{Context Containment}: Each collaboration phase operates within bounded context limits, preventing exponential growth typical in dialogue-based approaches.

\textbf{Information Density}: Communication focuses on specific, actionable content rather than verbose explanations, maximizing information value.

\textbf{Structured Interaction}: Agents interact through predefined formats (drafts, annotations, evaluations) rather than open-ended conversations, maintaining focus and clarity.

\subsubsection{Draft as Communication Medium}
Our approach employs concise drafts as structured communication intermediates for multi-agent reasoning tasks. Unlike traditional approaches where agents directly exchange lengthy messages, our agents collaborate through a structured three-phase process. First, agents create focused drafts where initial solutions are generated concisely while maintaining both clarity and completeness. Second, agents provide targeted annotations by suggesting improvements through specific, bounded feedback rather than lengthy explanations. Third, this approach enables indirect collaboration where agents see each other's work through the shared draft rather than through direct communication channels.

This design eliminates the context explosion problem while preserving the collaborative benefits of multiple perspectives.

\subsection{Efficient Agent Specialization}

DMC employs three specialized agents coordinated by a centralized orchestration framework. The orchestration framework manages workflow execution, resource allocation, and state coordination, but does not generate content or make reasoning decisions---these functions are exclusively handled by the specialized agents.

\subsubsection{Role-Based Collaboration}
To maximize coordination while maintaining quality, each agent is assigned a well-defined responsibility. The \textit{Worker Agents} contribute task-specific knowledge and generate initial solutions, the \textit{Merger Agent} integrates structured annotations into coherent draft updates, and the \textit{Leader Agent} evaluates quality and provides termination decisions. Clear boundaries among these roles prevent scope creep and keep the collaborative focus sharp.

\subsubsection{Structured Annotation System}
Instead of verbose free-form discussions, our annotation system enforces a concise feedback protocol with structured categories, priority levels, and bounded length (see Listing~\ref{lst:gsm8k_json}). This structure concentrates the agents’ attention on high-impact edits while keeping context growth under control.

\begin{algorithm}[t]
\caption{Draft-Mediated Collaboration Protocol}
\label{alg:dmc}
\begin{algorithmic}[1]
\REQUIRE Task prompt $q$, task type $t$, agent list $A$
\STATE Initialize empty draft $d \leftarrow \emptyset$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $d_a \leftarrow$ \textsc{GenerateDraft}$(a,q,t)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{SelectBestDraft}$\big(\{d_a\}\big)$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $\alpha_a \leftarrow$ \textsc{AnnotateDraft}$(a,d)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{MergeAgent}$\big(d,\{\alpha_a\}\big)$
\STATE $q_s \leftarrow$ \textsc{LeaderEvaluate}$(d)$
\IF{$q_s < \tau$}
\STATE Provide feedback and repeat Lines~2--10 (max 2 rounds)
\ENDIF
\RETURN Final answer extracted from $d$
\end{algorithmic}
\end{algorithm}

\begin{listing}[tb]
\caption{Example JSON annotation (GSM8K)}\label{lst:gsm8k_json}
\begin{lstlisting}[numbers=none]
[
  {"span_id": 0, "type": "completeness", "priority": 3,
   "suggestion": "Consider adding a brief introduction to explain the problem and the goal, to improve clarity and context for the step-by-step solution."}
]
\end{lstlisting}
\end{listing}

The schema fields guide downstream processing. The \texttt{span\_id} uniquely identifies a text segment, \texttt{type} categorises the feedback (\texttt{accuracy}, \texttt{clarity}, \texttt{completeness}, or \texttt{typo}), \texttt{priority} (1--3) indicates urgency, and \texttt{suggestion} contains the proposed edit in concise form. Each JSON list maintains brevity to ensure focused communication.

\subsection{Intelligent Synthesis Without Dialogue}

\subsubsection{Consensus-Driven Merge Mechanism}
The Merger agent implements a consensus-driven integration process where annotations require peer agreement before application. Workers first propose annotations with \texttt{pending} status, then peers provide numerical agreement responses (0-1 scale). Annotations achieve \texttt{consensus} status when average agreement $\geq$ 0.8 with minimum 2 responses. The system performs three-tier validation (status, score, response count) before executing LLM-driven semantic integration of only consensus-approved annotations. This prevents single-agent dominance while maintaining collaborative decision-making without dialogue overhead.

\subsubsection{Context-Aware Quality Assessment}
The Leader agent applies multi-dimensional scoring---covering accuracy, completeness, and clarity---then returns focused feedback. This targeted guidance steers subsequent refinement rounds without triggering a full re-discussion.

\subsection{Adaptive Collaboration Intensity}

\subsubsection{Task-Complexity-Based Scaling}
Our framework automatically adjusts collaboration depth based on task requirements, optimizing the coordination-quality tradeoff:

\textbf{Minimal Collaboration} (Simple tasks): Single-round annotation with basic quality checks.

\textbf{Standard Collaboration} (Moderate tasks): Full annotation cycle with semantic merging.

\textbf{Enhanced Collaboration} (Complex tasks): Multiple refinement rounds with quality-driven iteration.

This adaptive approach ensures appropriate coordination while maintaining quality for tasks that require deeper collaboration.

\subsubsection{Domain-Specific Optimization}
We implement task-specific coordination optimizations:

\textbf{Mathematical Reasoning}: Focus on step verification and numerical accuracy with minimal explanatory text.

\textbf{Code Generation}: Emphasize functional correctness and edge case handling through structured testing protocols.

\textbf{Reading Comprehension}: Leverage evidence extraction and logical inference with targeted fact verification.

\textbf{Knowledge Reasoning}: Implement systematic option evaluation with focused knowledge application.

Each domain optimization reduces irrelevant content while maintaining task-specific quality requirements.

\subsection{Context Management Mechanisms}

\subsubsection{Structured Communication}
We maintain organized communication through four complementary techniques. First, each interaction phase follows structured formats to keep communication focused. Second, reusable prompt templates ensure consistent interaction patterns. Third, progressive refinement encourages agents to improve drafts incrementally rather than regenerate them from scratch. Finally, an early-termination rule stops the collaboration once the solution exceeds quality thresholds, avoiding unnecessary iterations.

\subsubsection{Context Management}
Organization is further improved by propagating only essential information across rounds, maintaining focused draft content, and presenting appropriate information levels to different agent roles as needed.

\subsection{Failure Mode Prevention}

Drawing from the Multi-Agent System Failure Taxonomy (MASFT) \cite{cemri2025multi}, our framework explicitly addresses common failure patterns through structural design choices:

\textbf{Preventing Inter-Agent Misalignment:} The shared draft serves as a single source of truth, eliminating conversation reset (FM-2.1) and task derailment (FM-2.3). Structured annotations ensure that no agent input is ignored (FM-2.5), while bounded communication prevents information withholding (FM-2.4).

\textbf{Ensuring Proper Verification:} Our Leader agent provides systematic quality assessment, preventing both premature termination (FM-3.1) and incomplete verification (FM-3.2). The multi-dimensional scoring rubric ensures thorough evaluation across accuracy, completeness, and clarity dimensions.

\textbf{Maintaining System Coherence:} Role-based specialization prevents agents from violating their specifications (FM-1.2), while the orchestration framework maintains clear stopping conditions (FM-1.5) and prevents step repetition (FM-1.3) through structured iteration control.

% ---------- Theoretical Analysis ----------
\section{Theoretical Analysis}
\subsection{Communication Complexity}

\textbf{Notation.} Let $A$ be the number of worker agents, $N$ the average draft length, $L$ the maximum annotation length per agent, and $R$ the number of collaboration rounds. In our implementation, $L \in [100, 150]$ tokens and $N$ varies by task complexity, typically $N \in [500, 1500]$ tokens.

\textbf{Agent Count Assumption.} Throughout our analysis, we consider practical multi-agent scenarios where $A$ is a small constant (typically 2-5 agents), which is standard in collaborative reasoning tasks. For scenarios requiring larger agent counts, the linear scaling in $A$ remains advantageous compared to quadratic dialogue-based approaches.

\begin{lemma}[Per-Round Communication Cost]
\label{lem:per_round}
In one collaboration round, the total communication in DMC includes draft broadcasting, annotation collection, merging, and evaluation, bounded by $O(A \cdot N + A \cdot L)$ per round.
\end{lemma}

\textbf{Proof sketch.} Each round involves: (1) broadcasting draft to $A$ workers ($A \times N$ tokens), (2) collecting annotations ($A \times L$ tokens), (3) merging process ($N + A \times L$ tokens), and (4) leader evaluation ($N$ tokens). The total is $O(A \times N + A \times L) = O(A \cdot (N + L))$. \hfill $\square$

\begin{theorem}[Total Communication Complexity]
\label{thm:total}
DMC achieves structured communication with complexity $O(R \cdot A \cdot (N + L))$ where $R$ is task-adaptive (typically 2-4 rounds), compared to dialogue schemes that may require $O(R \times A^2 \times L)$ for full agent-to-agent communication.
\end{theorem}

\textbf{Proof sketch.} DMC uses centralized coordination where each agent communicates only through the shared draft, avoiding quadratic agent-to-agent communication. The draft-mediated approach ensures that information flows efficiently through the shared artifact rather than requiring all-to-all message passing. \hfill $\square$

\subsection{Temporal Complexity}

\begin{lemma}[Parallel Processing Advantage]
\label{lem:parallel}
DMC's annotation phase enables parallel agent processing, where $A$ agents simultaneously analyze the shared draft, resulting in temporal complexity $O(\max_i T_i)$ rather than $O(\sum_i T_i)$ for sequential dialogue systems, where $T_i$ is the processing time for agent $i$.
\end{lemma}

\textbf{Proof sketch.} In DMC, agents receive the same draft simultaneously and generate annotations independently. The total annotation time is bounded by the slowest agent rather than the sum of all agent processing times. This contrasts with dialogue systems where agents must wait for previous speakers, creating sequential dependencies. \hfill $\square$

\begin{theorem}[Dual Efficiency Scaling]
\label{thm:dual_efficiency}
DMC achieves dual efficiency scaling: $O(A \cdot (N + L))$ communication complexity and $O(\max_i T_i)$ temporal complexity, both independent of quadratic agent interaction patterns characteristic of dialogue-based systems.
\end{theorem}

\paragraph{Implications.} DMC's dual efficiency advantage---linear token scaling and parallel time complexity---provides compound benefits that increase with team size. The centralized coordination through shared drafts avoids both communication explosion and temporal bottlenecks inherent in dialogue-based schemes. We verify these theoretical predictions empirically through comprehensive dual-dimensional analysis in Section~\ref{sec:results}.

% ===== 接下来保持原来的 Experimental Setup =====

\section{Experimental Setup}

We evaluate our draft-mediated collaboration framework against both single-agent baselines and existing multi-agent approaches across eight diverse benchmarks. Our evaluation focuses on performance quality and communication structure metrics.

\subsection{Benchmark Datasets}
\textbf{Mathematical Reasoning}: GSM8K \cite{cobbe2021training} (grade school math) and MATH \cite{hendrycks2021math} (competition mathematics) test numerical reasoning capabilities.

\textbf{Code Generation}: HumanEval \cite{chen2021evaluating} and MBPP \cite{austin2021program} evaluate programming problem-solving and functional correctness.

\textbf{Reading Comprehension}: HotpotQA \cite{yang2018hotpotqa} assesses information extraction and multi-hop reasoning.

\textbf{Knowledge Reasoning}: MMLU \cite{hendrycks2021measuring}, GPQA \cite{dhingra2022gpqa}, and StrategyQA \cite{geva2021strategyqa} test broad knowledge application and logical reasoning.

\subsection{Communication Metrics}
Beyond standard accuracy measures, we evaluate the structure and organization of agent interactions to understand communication patterns within our framework. We analyze context length distributions by measuring both maximum and average context lengths during processing, ensuring that our bounded communication protocol maintains predictable resource consumption. Additionally, we measure wall-clock processing time for collaborative reasoning to assess the practical efficiency of our multi-agent coordination compared to single-agent baselines.

\subsection{Model Configuration}
Our framework orchestrates multiple LLM providers to ensure reproducibility across different model architectures. We evaluate using four widely accessible models: OpenAI's \texttt{gpt-4o-mini}, Anthropic's \texttt{claude-3-5-haiku}, \texttt{llama-3.3-70b}, and Google's \texttt{gemini-2.0-flash}. Temperature settings are task-optimized: DMC uses temperature=0.3-0.4 for generation tasks to balance creativity and consistency, while all evaluation tasks use temperature=0.1 for deterministic assessment. Baseline methods use their respective optimal settings (CoT-SC: 0.7 for generation, LLM-Debate: 0.3 throughout).

\subsection{Baseline Comparisons}
We compare DMC against a comprehensive suite of baselines. These include single-agent methods like direct prompting and Chain-of-Thought (CoT); more advanced structured reasoning techniques such as Tree-of-Thoughts (ToT)~\cite{yao2024tree}, Graph-of-Thoughts (GoT)~\cite{besta2024graph}, and Chain-of-Draft (CoD)~\cite{xu2025cod}; and several multi-agent frameworks. The latter category encompasses dialogue-based systems~\cite{chen2023multi,du2023improving}, pruning-based collectives such as AgentPrune~\cite{zhang2024cut}, self-improving strategies like DyLAN~\cite{liu2024dynamic}, swarm-style collaboration like GPT-Swarm~\cite{frendo2023gptswarm}, and automated workflow generation such as AFLOW~\cite{zhang2024aflow}. We also report results for simple aggregation baselines (e.g., majority voting) and ablated versions of our own approach.

\section{Results}
\label{sec:results}

\begin{table*}[t]
    \centering
    \caption{Main results on eight benchmarks (100 problems each). We report accuracy (0-1 scale) for most tasks, Pass@1 for code generation, and F1 score for reading comprehension. DMC achieves strong performance across all evaluated tasks.}
    \label{tab:main_results}
    \begin{tabular}{lcccccccc}
        \hline
        \textbf{Method} & \textbf{GSM8K} & \textbf{MATH} & \textbf{HotpotQA} & \textbf{HumanEval} & \textbf{MBPP} & \textbf{MMLU} & \textbf{GPQA} & \textbf{StrategyQA}\\
        \hline
        Direct & 0.81 & 0.34 & 0.35 & 0.71 & 0.99 & 0.33 & 0.41 & 0.71\\
        CoT & 0.94 & 0.45 & 0.69 & 0.77 & 0.99 & 0.38 & 0.44 & 0.71\\
        CoT-SC & 0.93 & 0.54 & 0.71 & 0.80 & 0.99 & 0.40 & 0.46 & 0.73\\
        CoD & 0.90 & 0.19 & 0.06 & 0.06 & 0.90 & 0.25 & 0.27 & 0.44\\
        LLM-Debate & 0.97 & 0.22 & 0.68 & 0.74 & 0.95 & 0.24 & 0.39 & 0.77\\
        AgentPrune & 0.93 & 0.45 & 0.56 & 0.80 & 0.99 & 0.56 & 0.32 & 0.63\\
        DyLAN & 0.75 & 0.69 & 0.20 & 0.57 & 0.92 & 0.32 & 0.40 & 0.37\\
        AFLOW & 0.81 & 0.63 & 0.23 & 0.85 & 1.00 & 0.18 & 0.39 & 0.73\\
        GPT-Swarm & 0.95 & 0.45 & 0.79 & 0.84 & 0.98 & 0.70 & 0.40 & 0.75\\
        \textbf{DMC (Ours)} & 0.98 & 0.66 & 0.77 & 0.87 & 1.00 & 0.76 & 0.49 & 0.83\\
        \hline
    \end{tabular}
\end{table*}



DMC achieves strong performance across eight diverse tasks, with notable results including 0.98 on GSM8K, 1.00 on MBPP, and 0.83 on StrategyQA. \textbf{Important note:} Our MBPP result of 1.00 (100\% Pass@1) is evaluated on a subset of 100 problems, which may not be directly comparable to community benchmarks typically evaluated on larger test sets. Beyond accuracy gains, DMC demonstrates substantial efficiency advantages in single-round execution: 66.4\% token reduction versus CoT Self-Consistency, 85.6\% reduction versus LLM-Debate, and 60.5\% reduction versus Multi-Agent CoT-SC, achieving 1,214±95 tokens per problem compared to 3,616±917 for CoT-SC, 8,453±2,843 for LLM-Debate, and 3,074±901 for MA-CoT-SC, while also providing significant time savings across all baselines (Section~\ref{sec:single_round_efficiency}). The efficiency stems from DMC's bounded annotation protocol, which prevents context explosion while maintaining collaborative benefits. Systematic ablation studies (Section~\ref{sec:ablation}) confirm that each DMC component contributes significantly to these efficiency gains, with the merger component providing the largest accuracy improvement (16-point gain on MATH) and context compression maintaining efficiency while preserving performance across difficulty levels.

\subsection{Single-Round Efficiency Analysis}
\label{sec:single_round_efficiency}

To evaluate DMC's computational efficiency, we conduct comprehensive single-round comparisons against prominent multi-agent reasoning baselines. We test across diverse reasoning tasks using identical problem instances and model configurations (gpt-4o-mini, claude-3-5-haiku, llama-3.3-70b, gemini-2.0-flash).

\textbf{Experimental Setup:} Each method processes the same set of problems in exactly one complete reasoning round to ensure fair comparison. All four methods are inherently single-round systems that execute their full designed protocols within one reasoning cycle:

\textbf{DMC}: Executes one complete collaboration round (Algorithm~\ref{alg:dmc}) with up to 3 internal collaboration steps, terminating early when quality thresholds are met (typically 1-2 steps).

\textbf{CoT Self-Consistency}: Performs one complete consistency round involving 3 independent sample generations followed by consistency checking and final answer selection.

\textbf{MA-CoT-SC}: Multiple agents each generate independent reasoning chains, with an aggregator selecting the most consistent solution from diverse agent perspectives.

\textbf{LLM-Debate}: Conducts one complete debate round with 3 mandatory phases: (1) Independent answer generation, (2) Cross-agent debate, and (3) Judge-based final decision.

\textbf{DMC}: Executes one complete draft-mediated collaboration cycle with structured annotation phases: worker draft generation, collaborative annotation, adaptive merging, and leader evaluation.

\textbf{Single-Round Clarification:} All four methods are inherently single-round systems by design. ``Single round'' refers to one complete execution of each method's full protocol: DMC executes one collaboration cycle with internal coordination steps, CoT-SC performs one consistency checking cycle with multiple samples, MA-CoT-SC conducts one multi-agent reasoning cycle with aggregation, and LLM-Debate conducts one complete debate cycle with three phases. This is not an experimental limitation but reflects the natural operating mode of these systems. DMC's efficiency advantage stems from its structured collaboration design that achieves high quality with fewer tokens per reasoning cycle.

\textbf{Method Comparison:} The four methods represent different paradigms: CoT-SC uses single-agent consistency checking, MA-CoT-SC employs multi-agent independent reasoning with aggregation, LLM-Debate relies on adversarial multi-agent discussion, while DMC uses structured collaborative annotation. This comparison reveals the efficiency trade-offs between different collaboration strategies.

\textbf{Results:} Figure~\ref{fig:efficiency_comparison} demonstrates that DMC achieves substantial efficiency gains across all baseline methods in single-round execution. DMC consumes 1,214±95 tokens per problem, achieving 3.0× efficiency over CoT-SC (3,616±917 tokens), 2.5× over MA-CoT-SC (3,074±901 tokens), and 7.0× over LLM-Debate (8,453±2,843 tokens). Beyond token efficiency, DMC provides significant time savings: 51.5\% faster than CoT-SC (19.9s vs 41.0s), 13.7\% faster than MA-CoT-SC (19.9s vs 23.1s), and 38.1\% faster than LLM-Debate (19.9s vs 32.1s). The comprehensive multi-dimensional analysis shows DMC's Pareto optimality in the efficiency correlation plot and demonstrates consistent superiority across token consumption, processing time, and resource efficiency metrics.

\textbf{Efficiency Analysis:} The efficiency gains stem from DMC's bounded annotation protocol that prevents context explosion while maintaining collaborative benefits. Token reduction percentages are calculated as $(T_{baseline} - T_{DMC})/T_{baseline} \times 100\%$: 66.4\% vs CoT-SC, 60.5\% vs MA-CoT-SC, and 85.6\% vs LLM-Debate. Notably, DMC achieves these gains while maintaining competitive accuracy, demonstrating that structured collaboration is more efficient than both single-agent consistency checking and multi-agent debate approaches.

\begin{figure}[t]
\centering
\includegraphics[width=\columnwidth]{efficiency.png}
\caption{Top panels show token/time distributions; bottom panels show correlation and relative cost. DMC achieves the best Pareto efficiency.}
\label{fig:efficiency_comparison}
\end{figure}

The comprehensive analysis reveals DMC's multi-dimensional efficiency superiority. The distribution plots demonstrate DMC's consistency with tight quartile ranges, while the efficiency correlation scatter plot positions DMC in the Pareto-optimal lower-left region, indicating simultaneous optimization of both token consumption and processing time. The relative efficiency ratios show that competing methods require 1.8×-4.3× more computational resources than DMC. This efficiency advantage stems from DMC's structured collaboration architecture, with bounded annotation preventing context explosion while maintaining collaborative benefits. The results reveal distinct efficiency profiles: CoT-SC suffers from high token consumption due to multiple independent samples, MA-CoT-SC shows moderate efficiency with multi-agent reasoning but lacks coordination structure, LLM-Debate exhibits the highest overhead due to verbose adversarial exchanges, while DMC achieves optimal efficiency through structured draft-mediated collaboration.

\subsection{Collaboration Efficiency Analysis}

% Combined Communication Analysis - dual panel
\begin{figure}[t]
\centering
\includegraphics[width=\columnwidth]{communication.png}
\caption{Left panels show token usage scaling and right panels show time efficiency analysis. DMC maintains linear scaling and consistent performance.}
\label{fig:communication_analysis}
\end{figure}

We analyze DMC's communication efficiency across multiple dimensions, comparing against three baseline methods in their natural single-round execution modes. Each method executes one complete reasoning cycle: DMC performs one collaboration round with internal coordination steps, CoT-SC executes one consistency cycle with 3 samples, MA-CoT-SC conducts multi-agent reasoning with aggregation, and LLM-Debate performs one complete debate cycle with 3 phases (independent generation, cross-agent debate, judge decision).



\subsubsection{Communication Complexity Scaling}
Figure~\ref{fig:communication_analysis} presents a comprehensive dual-dimensional analysis of DMC's communication efficiency. Panel (a) demonstrates token usage scaling patterns across agent configurations, while panel (b) reveals time efficiency characteristics based on real API call measurements.

\textbf{Token Complexity Analysis:} Our experimental analysis reveals three distinct scaling patterns. DMC maintains linear $O(A \cdot (N + L))$ communication complexity, exhibiting controlled growth from approximately 1,200 tokens (2 agents) to 1,800 tokens (5 agents). MA-CoT-SC shows moderate scaling with steeper growth from 1,400 to 2,800 tokens across the same range. LLM-Debate demonstrates quadratic $O(A^2)$ scaling, escalating from 2,200 tokens (2 agents) to 4,200 tokens (5 agents), confirming the theoretical communication explosion in dialogue-based systems.

\textbf{Time Efficiency Analysis:} Panel (b) reveals DMC's superior temporal performance, maintaining consistent processing times around 16-20 seconds regardless of team size. In contrast, LLM-Debate requires 30-37 seconds with increasing latency as agent count grows, while MA-CoT-SC shows intermediate performance at 22-27 seconds. This temporal advantage stems from DMC's parallel annotation protocol, where agents simultaneously process drafts rather than engaging in sequential dialogue exchanges.

The dual efficiency advantages are substantial and increase with team size. DMC achieves 73.7\% token reduction and 47\% time reduction vs LLM-Debate with 2 agents, improving to 80.0\% token reduction and 46\% time reduction with 5 agents. This demonstrates the fundamental scalability difference: as collaboration complexity increases, DMC's structured approach becomes increasingly advantageous across both computational and temporal dimensions.





\subsubsection{Communication Pattern Analysis}
The dual-dimensional analysis in Figure~\ref{fig:communication_analysis} reveals several key advantages of DMC's structured communication protocol.

\textbf{Computational Efficiency Patterns:} DMC's linear token scaling stems from its bounded annotation architecture. Each agent contributes a fixed-size annotation (L $\in$ [100, 150] tokens) regardless of team size, preventing the communication explosion characteristic of dialogue systems where message complexity grows with participant count. This architectural constraint maintains predictable resource consumption while preserving collaborative benefits.

\textbf{Temporal Efficiency Patterns:} The time analysis reveals DMC's parallel processing advantage. Unlike sequential dialogue systems where agents must wait for previous speakers, DMC enables simultaneous draft analysis and annotation. This parallelization, combined with the bounded context protocol, results in near-constant processing time regardless of team size---a critical scalability property for practical deployment.

\textbf{Scalability Implications:} The combined token-time analysis demonstrates that DMC's advantages compound with scale. While dialogue-based methods suffer from both computational and temporal overhead growth, DMC maintains efficiency across both dimensions. This dual efficiency is particularly valuable for resource-constrained environments where both API costs and response latency matter.

\subsection{Ablation Study}
\label{sec:ablation}

\begin{figure}[t]
\centering
\includegraphics[width=\columnwidth]{ablation.png}
\caption{Ablation study results across DMC variants. (a) Accuracy by dataset type shows Full DMC achieving 66\% on MATH and 98\% on GSM8K, with merger removal causing the largest performance drop. (b) Token usage vs accuracy trade-off reveals efficiency patterns, with bubble size indicating overall efficiency scores.}
\label{fig:ablation_efficiency}
\end{figure}

We conduct systematic ablation experiments across four DMC variants to validate each component's contribution: (1) \textbf{Full DMC} with all components active, (2) \textbf{w/o Merge} removing the adaptive merger, (3) \textbf{w/o Annotation} disabling collaborative annotations, and (4) \textbf{w/o Context Compression} removing context management. Results demonstrate clear performance hierarchies across datasets.

\textbf{Accuracy Analysis:} On MATH problems, Full DMC achieves 66\% accuracy, outperforming w/o Annotation (60\%), w/o Context Compression (60\%), and w/o Merge (50\%). The merger component shows the largest impact, with its removal causing a 16-point accuracy drop. On GSM8K, all variants achieve high performance (90-98\%), with Full DMC reaching 98\% accuracy, demonstrating robustness across difficulty levels.

\textbf{Efficiency Trade-offs:} The token usage analysis reveals interesting patterns. Full DMC uses 1,794 tokens/problem on average, while ablated variants show varied efficiency: w/o Annotation (1,724 tokens), w/o Merge (1,783 tokens), and w/o Context Compression (1,834 tokens). Notably, removing annotations slightly reduces token usage while maintaining reasonable accuracy, suggesting efficient annotation protocols. The context compression component shows clear value, with its removal increasing token consumption by 2.2\% while reducing accuracy.

\textbf{Component Contributions:} Each component serves distinct purposes: the merger integrates diverse perspectives for complex reasoning, annotations provide targeted feedback without dialogue overhead, and context compression maintains efficiency at scale. The results validate DMC's design principle that structured collaboration outperforms both single-agent approaches and unstructured multi-agent dialogue.

\section{Discussion \& Limitations}

DMC represents a paradigm shift from dialogue-based to artifact-centric multi-agent collaboration, transforming coordination from managing conversation complexity to optimizing draft evolution. Unlike Chain of Draft~\cite{xu2025cod} for single agents, DMC addresses multi-agent communication scaling, achieving 98.0\% accuracy on GSM8K with structured patterns. Our approach addresses critical failure modes identified in MASFT taxonomy~\cite{cemri2025multi}, preventing conversation reset and task derailment through draft-focused interaction.

\textbf{Limitations and Evaluation Scope.} Our evaluation is conducted on subsets of 100 problems per benchmark, which may not fully represent performance on complete test sets. The reported MBPP score of 100\% should be interpreted within this limited evaluation scope. Additionally, limitations include potential merger struggles with conflicting annotations and hand-tuned annotation caps requiring adaptive allocation. Future work should explore optimal annotation strategies, hierarchical draft structures, evaluation on full benchmark datasets, and multimodal extensions beyond text to code and diagrams, advancing AI collaborative problem-solving.

% References and End of Paper
% These lines must be placed at the end of your paper
\bibliography{templates/aaai2026}

\end{document}
