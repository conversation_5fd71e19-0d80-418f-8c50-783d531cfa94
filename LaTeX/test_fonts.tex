\documentclass[letterpaper]{article}
\usepackage[submission]{aaai2026}
\usepackage{times}
\usepackage{helvet}
\usepackage{courier}
\usepackage[hyphens]{url}
\usepackage{graphicx}
\urlstyle{rm}
\def\UrlFont{\rm}
\usepackage{natbib}
\usepackage{caption}
\frenchspacing
\setlength{\pdfpagewidth}{8.5in}
\setlength{\pdfpageheight}{11in}

\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{microtype}
\usepackage{amsthm}
\usepackage[T1]{fontenc}
\usepackage{textcomp}
\theoremstyle{definition}
\newtheorem{lemma}{Lemma}
\newtheorem{theorem}{Theorem}

\title{Font Test Document}
\author{Test Author}

\begin{document}
\maketitle

\section{Temporal Complexity}

\begin{lemma}[Parallel Processing Advantage]
\label{lem:parallel}
DMC's annotation phase enables parallel agent processing, where $A$ agents simultaneously analyze the shared draft, resulting in temporal complexity $O(\max_i T_i)$ rather than $O(\sum_i T_i)$ for sequential dialogue systems, where $T_i$ is the processing time for agent $i$.
\end{lemma}

\textbf{Proof sketch.} In DMC, agents receive the same draft simultaneously and generate annotations independently. The total annotation time is bounded by the slowest agent rather than the sum of all agent processing times. This contrasts with dialogue systems where agents must wait for previous speakers, creating sequential dependencies. \hfill $\square$

\begin{theorem}[Dual Efficiency Scaling]
\label{thm:dual_efficiency}
DMC achieves dual efficiency scaling: $O(N + A \times L)$ communication complexity and $O(\max_i T_i)$ temporal complexity, both independent of quadratic agent interaction patterns characteristic of dialogue-based systems.
\end{theorem}

\textbf{Test of various fonts:}
\begin{itemize}
\item Regular text: This is normal text
\item \textit{Italic text: This is italic text}
\item \textbf{Bold text: This is bold text}
\item Math: $O(\max_i T_i)$ and $O(\sum_i T_i)$
\item Mixed: \textit{DMC's annotation phase} with math $A \times L$
\end{itemize}

\end{document}
