#!/usr/bin/env python3
"""
Full DMC Efficiency Comparison
使用完整DMC系统的效率对比实验（单轮优化）
重点展示DMC在token使用和处理时间上的效率优势
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any, Optional
import statistics
from pathlib import Path
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
# from scipy import stats  # 暂时注释掉scipy依赖

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion
from utils.token_utils import TokenCounter
from utils.efficiency_tracker import EfficiencyTracker, OperationType

class FullDMCEfficiencyTest:
    """完整DMC系统效率测试（优化单轮性能）"""
    
    def __init__(self):
        self.agents = ["openai", "gemini", "llama"]
        self.token_counter = TokenCounter()
        self.efficiency_tracker = EfficiencyTracker()
        
        # 扩展测试问题集（更多样化）
        self.test_questions = [
            # 数学问题
            "Janet's ducks lay 16 eggs per day. She eats 3 for breakfast and bakes muffins with 4. She sells the remainder for $2 each. How much does she make daily?",
            "Find the value of x such that sqrt(x+7) = 9.",
            "A rectangle has a length of 12 cm and width of 8 cm. What is its area and perimeter?",
            
            # 逻辑推理
            "If a train travels 120 miles in 2 hours, what is its average speed?",
            "All birds can fly. Penguins are birds. Can penguins fly?",
            
            # 编程问题
            "Write a Python function that returns the factorial of a number.",
            "How do you reverse a string in Python without using built-in functions?",
            
            # 常识问题
            "What are the main causes of climate change?",
            "Explain the difference between renewable and non-renewable energy sources.",
            "Why do leaves change color in autumn?"
        ]
    
    async def test_full_dmc_system(self, question: str):
        """测试完整DMC系统（使用真实的coordination/controller）"""
        
        start_time = time.time()
        
        try:
            # 导入完整的DMC控制器
            from coordination.controller import SimplifiedCollaborativeController
            
            print(f"   🤝 Running Full DMC System...")
            
            # 初始化控制器
            controller = SimplifiedCollaborativeController()
            
            # 使用完整的DMC流程处理任务
            result = await controller.process_task(
                question=question,
                task_type="efficiency_test",
                available_agents=self.agents,
                problem_id=f"efficiency_test_{int(time.time())}"
            )
            
            # 从结果中提取效率信息
            solution = result.get('solution', '')
            total_tokens = result.get('total_tokens', 0)
            quality_score = result.get('quality_score', 0.0)
            processing_mode = result.get('processing_mode', 'unknown')
            
            # 如果没有token信息，手动计算
            if total_tokens == 0:
                # 从efficiency tracker获取信息
                if hasattr(controller, 'efficiency_tracker'):
                    tracker_data = controller.efficiency_tracker.get_efficiency_summary()
                    total_tokens = tracker_data.get('total_tokens', 0)
                
                # 如果还是没有，使用简单估算
                if total_tokens == 0:
                    input_tokens = self.token_counter.count_tokens(question)
                    output_tokens = self.token_counter.count_tokens(solution)
                    # 估算中间过程token（基于agents数量）
                    estimated_process_tokens = len(self.agents) * input_tokens * 2
                    total_tokens = input_tokens + output_tokens + estimated_process_tokens
            
            duration = time.time() - start_time
            
            print(f"     ✅ DMC: {total_tokens} tokens, {duration:.2f}s, quality: {quality_score:.2f}")
            
            return {
                'solution': solution,
                'tokens': total_tokens,
                'time': duration,
                'quality_score': quality_score,
                'processing_mode': processing_mode,
                'success': result.get('success', True)
            }
            
        except Exception as e:
            print(f"     ❌ Full DMC failed: {e}")
            duration = time.time() - start_time
            return {
                'solution': '',
                'tokens': 0,
                'time': duration,
                'quality_score': 0.0,
                'processing_mode': 'failed',
                'success': False,
                'error': str(e)
            }
    
    async def test_cot_self_consistency(self, question: str, num_samples: int = 3):
        """测试CoT Self-Consistency（优化版）"""
        
        total_tokens = 0
        start_time = time.time()
        
        print(f"   🧠 Running CoT-SC (samples: {num_samples})...")
        
        cot_prompt = f"""Solve this problem step by step. Think through each step carefully and show your reasoning.

Problem: {question}

Let me think step by step:"""
        
        responses = []
        
        # 多次采样
        for i in range(num_samples):
            try:
                response = await async_generate_completion(
                    agent_id="openai",
                    prompt=cot_prompt,
                    system_prompt="You are an expert problem solver. Think step by step and show your reasoning clearly.",
                    temperature=0.7  # 更高温度获得多样性
                )
                
                responses.append(response)
                
                input_tokens = self.token_counter.count_tokens(cot_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                print(f"     ⚠️ CoT sample {i+1} failed: {e}")
                continue
        
        # 一致性判断
        if len(responses) > 1:
            consistency_prompt = f"""Here are {len(responses)} different solutions to the same problem:

Problem: {question}

Solutions:
""" + "\n\n".join([f"Solution {i+1}: {resp}" for i, resp in enumerate(responses)]) + """

Determine the most consistent and correct answer:"""
            
            try:
                final_answer = await async_generate_completion(
                    agent_id="openai",
                    prompt=consistency_prompt,
                    system_prompt="Choose the most consistent and correct solution.",
                    temperature=0.3
                )
                
                input_tokens = self.token_counter.count_tokens(consistency_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                print(f"     ⚠️ CoT consistency failed: {e}")
                final_answer = responses[0] if responses else ""
        else:
            final_answer = responses[0] if responses else ""
        
        duration = time.time() - start_time
        print(f"     ✅ CoT-SC: {total_tokens} tokens, {duration:.2f}s")
        
        return {
            'solution': final_answer,
            'tokens': total_tokens,
            'time': duration,
            'samples_generated': len(responses),
            'success': len(responses) > 0
        }
    
    async def test_llm_debate(self, question: str):
        """测试LLM-Debate系统（优化版）"""
        
        total_tokens = 0
        start_time = time.time()
        
        print(f"   🥊 Running LLM-Debate...")
        
        # Phase 1: Independent answers
        initial_answers = {}
        
        tasks = []
        for agent in self.agents:
            prompt = f"Solve this problem independently:\n\n{question}\n\nProvide your complete solution:"
            
            task = async_generate_completion(
                agent_id=agent,
                prompt=prompt,
                system_prompt="You are an expert. Solve problems independently.",
                temperature=0.3
            )
            tasks.append((agent, prompt, task))
        
        # 并行执行初始回答
        results = await asyncio.gather(*[task for _, _, task in tasks], return_exceptions=True)
        
        for i, ((agent, prompt, _), result) in enumerate(zip(tasks, results)):
            if not isinstance(result, Exception):
                initial_answers[agent] = result
                input_tokens = self.token_counter.count_tokens(prompt)
                output_tokens = self.token_counter.count_tokens(result)
                total_tokens += input_tokens + output_tokens
        
        if len(initial_answers) < 2:
            duration = time.time() - start_time
            print(f"     ❌ LLM-Debate: insufficient initial answers")
            return {
                'solution': '',
                'tokens': total_tokens,
                'time': duration,
                'success': False
            }
        
        # Phase 2: Debate phase（并行）
        debate_tasks = []
        
        for agent in self.agents:
            if agent not in initial_answers:
                continue
                
            others_answers = []
            for other_agent, answer in initial_answers.items():
                if other_agent != agent:
                    others_answers.append(f"{other_agent.upper()}: {answer}")
            
            others_text = "\n\n".join(others_answers)
            
            debate_prompt = f"""Original problem: {question}

Your initial answer: {initial_answers[agent]}

Other agents' answers:
{others_text}

Now engage in debate and provide your refined final answer:"""
            
            task = async_generate_completion(
                agent_id=agent,
                prompt=debate_prompt,
                system_prompt="Engage in constructive debate and refine your answer.",
                temperature=0.3
            )
            debate_tasks.append((agent, debate_prompt, task))
        
        # 并行执行辩论
        debate_results = await asyncio.gather(*[task for _, _, task in debate_tasks], return_exceptions=True)
        
        debate_responses = {}
        for i, ((agent, prompt, _), result) in enumerate(zip(debate_tasks, debate_results)):
            if not isinstance(result, Exception):
                debate_responses[agent] = result
                input_tokens = self.token_counter.count_tokens(prompt)
                output_tokens = self.token_counter.count_tokens(result)
                total_tokens += input_tokens + output_tokens
        
        # Phase 3: Final decision
        final_answer = ""
        if debate_responses:
            try:
                judge_agent = self.agents[0]
                
                all_debates = []
                for agent, response in debate_responses.items():
                    all_debates.append(f"{agent.upper()}: {response}")
                
                debates_text = "\n\n".join(all_debates)
                
                final_prompt = f"""Original problem: {question}

All agents' debate responses:
{debates_text}

As the judge, synthesize the best solution from all the debates:"""
                
                final_answer = await async_generate_completion(
                    agent_id=judge_agent,
                    prompt=final_prompt,
                    system_prompt="You are the judge. Synthesize the best solution from all debates.",
                    temperature=0.3
                )
                
                input_tokens = self.token_counter.count_tokens(final_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                print(f"     ⚠️ LLM-Debate judge failed: {e}")
                final_answer = list(debate_responses.values())[0] if debate_responses else ""
        
        duration = time.time() - start_time
        print(f"     ✅ LLM-Debate: {total_tokens} tokens, {duration:.2f}s")
        
        return {
            'solution': final_answer,
            'tokens': total_tokens,
            'time': duration,
            'initial_answers': len(initial_answers),
            'debate_responses': len(debate_responses),
            'success': len(debate_responses) > 0
        }

    def create_enhanced_visualizations(self, all_results, num_runs=1):
        """创建增强的效率对比可视化"""

        # 准备数据
        df_data = []
        for method_name, method_results in all_results.items():
            for result in method_results:
                df_data.append({
                    'Method': method_name,
                    'Tokens': result['tokens'],
                    'Time': result['time'],
                    'Success': result.get('success', True)
                })

        df = pd.DataFrame(df_data)

        if df.empty:
            print("❌ No data for visualization")
            return

        # 只保留成功的结果
        df_success = df[df['Success'] == True].copy()
        
        if df_success.empty:
            print("❌ No successful results for visualization")
            return

        # 计算统计信息
        summary = df_success.groupby('Method').agg({
            'Tokens': ['mean', 'std', 'count'],
            'Time': ['mean', 'std', 'count']
        }).round(2)

        summary.columns = ['tokens_mean', 'tokens_std', 'tokens_count', 'time_mean', 'time_std', 'time_count']
        summary = summary.reset_index()

        # 计算置信区间 (95%) - 简化版本，不使用scipy
        for idx, row in summary.iterrows():
            n_tokens = row['tokens_count']
            n_time = row['time_count']
            
            if n_tokens > 1:
                # 使用简化的1.96近似（对于n>30）或2.0（对于小样本）
                t_val = 2.0 if n_tokens < 30 else 1.96
                tokens_ci = t_val * row['tokens_std'] / np.sqrt(n_tokens)
                summary.at[idx, 'tokens_ci'] = tokens_ci
            else:
                summary.at[idx, 'tokens_ci'] = 0
                
            if n_time > 1:
                t_val = 2.0 if n_time < 30 else 1.96
                time_ci = t_val * row['time_std'] / np.sqrt(n_time)
                summary.at[idx, 'time_ci'] = time_ci
            else:
                summary.at[idx, 'time_ci'] = 0

        # 设置图表样式
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.size': 16,
            'axes.titlesize': 18,
            'axes.labelsize': 16,
            'xtick.labelsize': 14,
            'ytick.labelsize': 14,
            'legend.fontsize': 14,
            'font.weight': 'bold',
            'axes.linewidth': 2,
            'grid.linewidth': 1,
        })

        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)

        # 1. 主要效率对比图（带置信区间）
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        colors = ['#A23B72', '#2E86AB', '#F18F01']  # DMC用粉紫色，其他方法用蓝色和橙色

        # Token对比（带置信区间）
        bars1 = ax1.bar(summary['Method'], summary['tokens_mean'],
                       yerr=summary['tokens_ci'], capsize=5, alpha=0.8,
                       color=colors, edgecolor='black', linewidth=2)

        for bar, mean_val, ci_val in zip(bars1, summary['tokens_mean'], summary['tokens_ci']):
            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + ci_val + 100,
                    f'{int(mean_val)}±{int(ci_val)}', ha='center', va='bottom',
                    fontsize=12, fontweight='bold')

        ax1.set_title(f'Token Usage Comparison\n(Single Round, n={num_runs} runs per method)', fontweight='bold')
        ax1.set_ylabel('Average Tokens per Question', fontweight='bold')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # 时间对比（带置信区间）
        bars2 = ax2.bar(summary['Method'], summary['time_mean'],
                       yerr=summary['time_ci'], capsize=5, alpha=0.8,
                       color=colors, edgecolor='black', linewidth=2)

        for bar, mean_val, ci_val in zip(bars2, summary['time_mean'], summary['time_ci']):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + ci_val + 0.5,
                    f'{mean_val:.1f}±{ci_val:.1f}s', ha='center', va='bottom',
                    fontsize=12, fontweight='bold')

        ax2.set_title(f'Processing Time Comparison\n(Single Round, n={num_runs} runs per method)', fontweight='bold')
        ax2.set_ylabel('Average Time per Question (s)', fontweight='bold')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(results_dir / 'full_dmc_efficiency_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 效率比较表
        print(f"\n📊 EFFICIENCY COMPARISON RESULTS (n={num_runs} runs)")
        print("=" * 80)
        print(f"{'Method':<15} {'Tokens (mean±CI)':<20} {'Time (mean±CI)':<20} {'Success Rate':<15}")
        print("-" * 80)
        
        for _, row in summary.iterrows():
            method = row['Method']
            tokens_str = f"{row['tokens_mean']:.0f}±{row['tokens_ci']:.0f}"
            time_str = f"{row['time_mean']:.1f}±{row['time_ci']:.1f}s"
            success_rate = f"{row['tokens_count']}/{num_runs * len(self.test_questions)}"
            print(f"{method:<15} {tokens_str:<20} {time_str:<20} {success_rate:<15}")

        # 3. DMC效率优势计算
        if 'Full DMC' in summary['Method'].values:
            dmc_row = summary[summary['Method'] == 'Full DMC'].iloc[0]
            dmc_tokens = dmc_row['tokens_mean']
            dmc_time = dmc_row['time_mean']

            print(f"\n🎯 DMC EFFICIENCY ADVANTAGES:")
            print("-" * 50)
            
            for _, row in summary.iterrows():
                if row['Method'] != 'Full DMC':
                    token_reduction = (row['tokens_mean'] - dmc_tokens) / row['tokens_mean'] * 100
                    time_reduction = (row['time_mean'] - dmc_time) / row['time_mean'] * 100
                    
                    print(f"vs {row['Method']}:")
                    print(f"  Token reduction: {token_reduction:.1f}%")
                    print(f"  Time reduction: {time_reduction:.1f}%")

        print("✅ Enhanced visualizations saved:")
        print("  - full_dmc_efficiency_comparison.png")

        return summary

async def run_full_dmc_efficiency(num_runs: int = 3):
    """运行完整DMC效率对比实验（多次运行提高统计可信度）"""

    tester = FullDMCEfficiencyTest()

    print("🚀 FULL DMC EFFICIENCY COMPARISON")
    print(f"Running {num_runs} iterations for statistical significance")
    print("=" * 80)

    all_results = {
        'Full DMC': [],
        'CoT-SC': [],
        'LLM-Debate': []
    }

    # 多次运行实验
    for run in range(num_runs):
        print(f"\n🔄 RUN {run + 1}/{num_runs}")
        print("-" * 40)
        
        for i, question in enumerate(tester.test_questions):
            print(f"\n📝 Question {i+1}: {question[:60]}...")

            # 测试完整DMC
            try:
                dmc_result = await tester.test_full_dmc_system(question)
                if dmc_result['success']:
                    all_results['Full DMC'].append({
                        'tokens': dmc_result['tokens'],
                        'time': dmc_result['time'],
                        'question_id': i,
                        'run_id': run,
                        'success': True
                    })
            except Exception as e:
                print(f"     ❌ Full DMC failed: {e}")

            # 测试CoT-SC
            try:
                cotsc_result = await tester.test_cot_self_consistency(question)
                if cotsc_result['success']:
                    all_results['CoT-SC'].append({
                        'tokens': cotsc_result['tokens'],
                        'time': cotsc_result['time'],
                        'question_id': i,
                        'run_id': run,
                        'success': True
                    })
            except Exception as e:
                print(f"     ❌ CoT-SC failed: {e}")

            # 测试LLM-Debate
            try:
                debate_result = await tester.test_llm_debate(question)
                if debate_result['success']:
                    all_results['LLM-Debate'].append({
                        'tokens': debate_result['tokens'],
                        'time': debate_result['time'],
                        'question_id': i,
                        'run_id': run,
                        'success': True
                    })
            except Exception as e:
                print(f"     ❌ LLM-Debate failed: {e}")

    # 分析结果
    summary = tester.create_enhanced_visualizations(all_results, num_runs)

    # 保存详细结果
    output_file = Path("results/full_dmc_efficiency_results.json")
    output_file.parent.mkdir(exist_ok=True)

    results_data = {
        'timestamp': time.time(),
        'num_runs': num_runs,
        'num_questions': len(tester.test_questions),
        'test_questions': tester.test_questions,
        'detailed_results': all_results,
        'summary_table': summary.to_dict() if summary is not None else None
    }

    with open(output_file, 'w') as f:
        json.dump(results_data, f, indent=2)

    print(f"\n💾 Results saved to: {output_file}")
    return results_data

if __name__ == "__main__":
    import sys
    
    # 允许从命令行指定运行次数
    num_runs = 3
    if len(sys.argv) > 1:
        try:
            num_runs = int(sys.argv[1])
        except ValueError:
            print("Invalid number of runs, using default: 3")
    
    print(f"Starting efficiency test with {num_runs} runs per method...")
    asyncio.run(run_full_dmc_efficiency(num_runs))